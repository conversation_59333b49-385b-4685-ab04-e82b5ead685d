# CreatePlan.vue 参数更新总结

## 概述
根据新文档参数要求，更新了 `src/components/task/CreatePlan.vue` 组件的参数传递方式，从旧的字符串枚举值改为新的数字枚举值，并调整了数据结构。

## 主要变更

### 1. 任务类型 (task_type)
**旧参数:**
- `'immediate'` - 立即任务
- `'timed'` - 单次定时任务  
- `'recurring'` - 重复任务
- `'continuous'` - 连续任务

**新参数:**
- `-1` - 立即任务
- `-3` - 单次定时任务
- `-2` - 重复任务
- `-5` - 连续任务

### 2. 失控动作 (out_of_control_action_in_flight)
**旧参数:**
- `'return_home'` - 返航
- `'continue_task'` - 继续执行

**新参数:**
- `1` - 返航
- `2` - 继续执行

### 3. 任务精度 (wayline_precision_type)
**旧参数:**
- `'rtk'` - 高精度 RTK
- `'gps'` - GNSS

**新参数:**
- `1` - 高精度 RTK
- `0` - GNSS

### 4. 返航模式 (rth_mode)
**旧参数:**
- `'optimal'` - 智能高度
- `'preset'` - 设定高度

**新参数:**
- `0` - 智能高度
- `1` - 设定高度

### 5. 重复类型 (repeat_type)
**旧参数:**
- `'daily'` - 每天
- `'weekly'` - 每周
- `'absolute_monthly'` - 每月按日期
- `'relative_monthly'` - 每月按星期

**新参数:**
- `4` - 每几天
- `5` - 每几周
- `6` - 每几月（按日期）
- `7` - 每几月（按星期）

### 6. 新增字段
根据新文档添加了以下可选字段：
- `business_type?: number` - 任务的业务类型
- `cloud_to_cloud_id?: string` - 云云对接id
- `dow: number[]` - 一周中的第几天
- `push_c2c_organization_uuid?: string` - 云云对接推送到组织的uuid
- `subscription_alert_*_frequency?: number` - 各种告警频率
- `subscription_receive_type: string[]` - 订阅告警通知类型
- `subscription_user_id: string[]` - 订阅用户ID列表
- `tags: string[]` - 云云对接标签
- `wom?: number` - 一个月中的第几周

## 代码变更详情

### 1. 更新了选项配置
```javascript
const TaskStrategyOptions = [
  { label: '立即', value: -1 },
  { label: '单次定时', value: -3 },
  { label: '重复定时', value: -2 },
  { label: '连续执行', value: -5 },
];

const OutOfControlActionOptionsNew = [
  { label: '返航', value: 1 },
  { label: '继续执行', value: 2 },
]
```

### 2. 更新了条件判断
将所有字符串比较改为数字比较：
- `planBody.task_type === 'timed'` → `planBody.task_type === -3`
- `['recurring', 'continuous'].includes(planBody.task_type)` → `[-2, -5].includes(planBody.task_type)`
- `planBody.task_type === 'continuous'` → `planBody.task_type === -5`

### 3. 更新了数据转换逻辑
在 `onSubmit` 函数中：
- 对于单次定时任务，创建包含 `begin_at` 的任务类型对象
- 对于重复/连续任务，创建包含 `begin_at`, `end_at`, `interval` 等的任务类型对象
- 根据重复类型设置相应的 `dow`, `dom`, `wom` 等参数
- 转换时间格式为字符串数组（`extended_begin_at`, `continuous_task_periods`）

### 4. 修复了类型问题
- 修复了 `WaylineFile` 类型中缺少 `ext` 属性的问题
- 更新了默认值设置

## 兼容性说明
- 所有UI显示保持不变，用户体验无影响
- 仅后端API参数格式发生变化
- 保持了原有的验证逻辑和错误处理

## 测试建议
1. 测试所有任务类型的创建（立即、单次定时、重复、连续）
2. 测试不同精度模式的选择
3. 测试失控动作的设置
4. 测试重复规则的各种组合
5. 测试蛙跳任务的参数设置
